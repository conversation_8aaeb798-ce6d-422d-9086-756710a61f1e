class Product {
  final int id;
  final String name;
  final String description;
  final double price;
  final String imageUrl;
  final bool isPopular;
  final bool isRecommended;

  Product({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.imageUrl,
    this.isPopular = false,
    this.isRecommended = false,
  });
}

// قائمة منتجات وهمية لاختبار العرض
final List<Product> demoProducts = [
  Product(
    id: 1,
    name: "Smartphone",
    description: "Latest 5G smartphone with high-end camera and fast processor.",
    price: 799.99,
    imageUrl: "https://via.placeholder.com/150x150.png?text=Smartphone",
    isPopular: true,
    isRecommended: true,
  ),
  Product(
    id: 2,
    name: "Wireless Headphones",
    description: "Noise-cancelling headphones with 40 hours battery life.",
    price: 149.99,
    imageUrl: "https://via.placeholder.com/150x150.png?text=Headphones",
    isPopular: true,
    isRecommended: false,
  ),
  Product(
    id: 3,
    name: "Gaming Laptop",
    description: "Powerful gaming laptop with RTX graphics and SSD storage.",
    price: 1299.00,
    imageUrl: "https://via.placeholder.com/150x150.png?text=Gaming+Laptop",
    isPopular: false,
    isRecommended: true,
  ),
];
